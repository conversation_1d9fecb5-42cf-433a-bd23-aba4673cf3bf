<?php

use App\Actions\Provider\ActivateProvider;
use App\Actions\Provider\ActivateProviders;
use App\Actions\Provider\ClmOnline\ForceProviderIntegrationToClmOnline;
use App\Actions\Provider\CreateProvider;
use App\Actions\Provider\DeleteProvider;
use App\Actions\Provider\DeleteProviders;
use App\Actions\Provider\EditProvider;
use App\Actions\Provider\ErpFlex\CreateProviderInErpFlex;
use App\Actions\Provider\GetProvider;
use App\Actions\Provider\GetProviders;
use App\Actions\Provider\GetProvidersByNameTradingNameOrTaxIdNumber;
use App\Actions\Provider\InactivateProvider;
use App\Actions\Provider\InactivateProviders;
use App\Actions\Provider\Soc\CreateProviderInSoc;
use App\Actions\Provider\Soc\GetProvidersFromSoc;
use App\Actions\ProviderAddress\CreateProviderAddress;
use App\Actions\ProviderAddress\DeleteProviderAddress;
use App\Actions\ProviderAddress\EditProviderAddress;
use App\Actions\ProviderAddress\GetProviderAddress;
use App\Actions\ProviderAddress\GetProviderAddresses;
use App\Actions\ProviderCityCoverageCity\CreateProviderCityCoverageCity;
use App\Actions\ProviderCityCoverageCity\DeleteProviderCityCoverageCities;
use App\Actions\ProviderCityCoverageCity\EditProviderCityCoverageCity;
use App\Actions\ProviderCityCoverageCity\GetProviderCityCoverageCity;
use App\Actions\ProviderCompany\CreateProviderCompany;
use App\Actions\ProviderCompany\DeleteProviderCompanies;
use App\Actions\ProviderCompany\EditProviderCompany;
use App\Actions\ProviderCompany\GetProviderCompany;
use App\Actions\ProviderContact\CreateProviderContact;
use App\Actions\ProviderContact\DeleteProviderContacts;
use App\Actions\ProviderContact\EditProviderContact;
use App\Actions\ProviderContact\GetProviderContact;
use App\Actions\ProviderExpense\ApproveProviderExpense;
use App\Actions\ProviderExpense\CreateProviderExpense;
use App\Actions\ProviderExpense\DeleteProviderExpense;
use App\Actions\ProviderExpense\EditProviderExpense;
use App\Actions\ProviderExpense\GetProviderExpense;
use App\Actions\ProviderExpense\GetProviderExpenses;
use App\Actions\ProviderExpense\RejectProviderExpense;
use App\Actions\ProviderExpenseFile\DownloadProviderExpenseFile;
use App\Actions\ProviderExpenseFile\GetProviderExpenseFiles;
use App\Actions\ProviderExpenseType\CreateProviderExpenseType;
use App\Actions\ProviderExpenseType\DeleteProviderExpenseType;
use App\Actions\ProviderExpenseType\EditProviderExpenseType;
use App\Actions\ProviderExpenseType\GetProviderExpenseType;
use App\Actions\ProviderExpenseType\GetProviderExpenseTypes;
use App\Actions\ProviderExpenseType\GetProviderExpenseTypesByProviderIdForSelect;
use App\Actions\ProviderFile\DeleteProviderFiles;
use App\Actions\ProviderQualification\CreateProviderQualification;
use App\Actions\ProviderQualification\DeleteProviderQualifications;
use App\Actions\ProviderQualification\EditProviderQualification;
use App\Actions\ProviderQualification\GetProviderQualification;
use App\Actions\ProviderQualification\GetProviderQualifications;
use App\Actions\ProviderProcedure\CreateProviderProcedure;
use App\Actions\ProviderProcedure\DeleteProviderProcedures;
use App\Actions\ProviderProcedure\EditProviderProcedure;
use App\Actions\ProviderProcedure\GetProviderProcedure;
use App\Actions\ProviderProcedure\GetProviderProcedures;
use App\Actions\ProviderProcedure\GetProviderProceduresByProviderId;
use App\Actions\ProviderProcedure\ImportDefaultProviderProceduresTableTemplate;
use App\Actions\ProviderQualificationCriteria\CreateProviderQualificationCriterion;
use App\Actions\ProviderQualificationCriteria\DeleteProviderQualificationCriteria;
use App\Actions\ProviderQualificationCriteria\EditProviderQualificationCriterion;
use App\Actions\ProviderQualificationCriteria\GetProviderQualificationCriteria;
use App\Actions\ProviderQualificationCriteria\GetProviderQualificationCriterion;

Route::prefix('providers')->group(function () {
    Route::get('', GetProviders::class)->name('providers.index');
    Route::post('get-from-soc', GetProvidersFromSoc::class)->name('providers.get_from_soc');
    Route::get('get-by-name-trading-name-or-tax-id-number', GetProvidersByNameTradingNameOrTaxIdNumber::class)->name('providers.get_by_name_trading_name_or_tax_id_number');
    Route::get('create', CreateProvider::class)->name('providers.create');
    Route::post('', CreateProvider::class)->name('providers.store');
    Route::post('activate', ActivateProviders::class)->name('providers.activate_batch');
    Route::post('inactivate', InactivateProviders::class)->name('providers.inactivate_batch');
    Route::delete('delete-batch', DeleteProviders::class)->name('providers.delete_batch');
    Route::prefix('{provider}')->group(function () {
        Route::get('', GetProvider::class)->name('providers.show');
        Route::get('edit', EditProvider::class)->name('providers.edit');
        Route::post('activate', ActivateProvider::class)->name('providers.activate');
        Route::post('inactivate', InactivateProvider::class)->name('providers.inactivate');
        Route::post('create-in-erp-flex', CreateProviderInErpFlex::class)->name('providers.create_in_erp_flex');
        Route::post('create-in-soc', CreateProviderInSoc::class)->name('providers.create_in_soc');
        Route::post('force-integration-to-clm-online', ForceProviderIntegrationToClmOnline::class)->name('providers.force_integration_to_clm_online');
        Route::put('', EditProvider::class)->name('providers.update');
        Route::delete('', DeleteProvider::class)->name('providers.destroy');
        Route::prefix('addresses')->group(function () {
            Route::get('index', GetProviderAddresses::class)->name('provider_addresses.index');
            Route::get('create', CreateProviderAddress::class)->name('provider_addresses.create');
            Route::post('', CreateProviderAddress::class)->name('provider_addresses.store');
            Route::prefix('{provider_address}')->group(function () {
                Route::get('', GetProviderAddress::class)->name('provider_addresses.show');
                Route::get('edit', EditProviderAddress::class)->name('provider_addresses.edit');
                Route::put('', EditProviderAddress::class)->name('provider_addresses.update');
                Route::delete('', DeleteProviderAddress::class)->name('provider_addresses.destroy');
            });
        });
        Route::prefix('contacts')->group(function () {
            Route::get('create', CreateProviderContact::class)->name('provider_contacts.create');
            Route::post('', CreateProviderContact::class)->name('provider_contacts.store');
            Route::delete('delete-batch', DeleteProviderContacts::class)->name('provider_contacts.delete_batch');
            Route::prefix('{provider_contact}')->group(function () {
                Route::get('', GetProviderContact::class)->name('provider_contacts.show');
                Route::get('edit', EditProviderContact::class)->name('provider_contacts.edit');
                Route::put('', EditProviderContact::class)->name('provider_contacts.update');
            });
        });
        Route::prefix('expense-types')->group(function () {
            Route::get('index', GetProviderExpenseTypes::class)->name('provider_expense_types.index');
            Route::get('get-by-provider-for-select', GetProviderExpenseTypesByProviderIdForSelect::class)->name('provider_expense_types.get_by_provider_for_select');
            Route::get('create', CreateProviderExpenseType::class)->name('provider_expense_types.create');
            Route::post('', CreateProviderExpenseType::class)->name('provider_expense_types.store');
            Route::prefix('{provider_expense_type}')->group(function () {
                Route::get('', GetProviderExpenseType::class)->name('provider_expense_types.show');
                Route::get('edit', EditProviderExpenseType::class)->name('provider_expense_types.edit');
                Route::put('', EditProviderExpenseType::class)->name('provider_expense_types.update');
                Route::delete('', DeleteProviderExpenseType::class)->name('provider_expense_types.destroy');
            });
        });
        Route::prefix('procedures')->group(function () {
            Route::get('', GetProviderProcedures::class)->name('provider_procedures.index');
            Route::get('get-by-provider', GetProviderProceduresByProviderId::class)->name('provider_expense_types.get_by_provider');
            Route::get('create', CreateProviderProcedure::class)->name('provider_procedures.create');
            Route::post('', CreateProviderProcedure::class)->name('provider_procedures.store');
            Route::post('import-default-table-template', ImportDefaultProviderProceduresTableTemplate::class)->name('provider_procedures.import_default_table_template');
            Route::delete('delete-batch', DeleteProviderProcedures::class)->name('provider_procedures.delete_batch');
            Route::prefix('{procedure}')->group(function () {
                Route::get('', GetProviderProcedure::class)->name('provider_procedures.show');
                Route::get('edit', EditProviderProcedure::class)->name('provider_procedures.edit');
                Route::put('', EditProviderProcedure::class)->name('provider_procedures.update');
            });
        });
        Route::prefix('provider-companies')->group(function () {
            Route::get('create', CreateProviderCompany::class)->name('provider_companies.create');
            Route::post('', CreateProviderCompany::class)->name('provider_companies.store');
            Route::delete('delete-batch', DeleteProviderCompanies::class)->name('provider_companies.delete_batch');
            Route::prefix('{provider_company}')->group(function () {
                Route::get('', GetProviderCompany::class)->name('provider_companies.show');
                Route::get('edit', EditProviderCompany::class)->name('provider_companies.edit');
                Route::put('', EditProviderCompany::class)->name('provider_companies.update');
            });
        });
        Route::prefix('city-coverage-cities')->group(function () {
            Route::get('create', CreateProviderCityCoverageCity::class)->name('provider_city_coverage_cities.create');
            Route::post('', CreateProviderCityCoverageCity::class)->name('provider_city_coverage_cities.store');
            Route::delete('delete-batch', DeleteProviderCityCoverageCities::class)->name('provider_city_coverage_cities.delete_batch');
            Route::prefix('{provider_city_coverage_city}')->group(function () {
                Route::get('', GetProviderCityCoverageCity::class)->name('provider_city_coverage_cities.show');
                Route::get('edit', EditProviderCityCoverageCity::class)->name('provider_city_coverage_cities.edit');
                Route::put('', EditProviderCityCoverageCity::class)->name('provider_city_coverage_cities.update');
            });
        });
        Route::prefix('files')->group(function () {
            Route::delete('delete-batch', DeleteProviderFiles::class)->name('provider_files.delete_batch');
        });
    });
});

Route::prefix('provider-expenses')->group(function () {
    Route::get('', GetProviderExpenses::class)->name('provider_expenses.index');
    Route::get('create', CreateProviderExpense::class)->name('provider_expenses.create');
    Route::post('', CreateProviderExpense::class)->name('provider_expenses.store');
    Route::prefix('{provider_expense}')->group(function () {
        Route::get('', GetProviderExpense::class)->name('provider_expenses.show');
        Route::get('edit', EditProviderExpense::class)->name('provider_expenses.edit');
        Route::post('approve', ApproveProviderExpense::class)->name('provider_expenses.approve');
        Route::post('reject', RejectProviderExpense::class)->name('provider_expenses.reject');
        Route::put('', EditProviderExpense::class)->name('provider_expenses.update');
        Route::delete('', DeleteProviderExpense::class)->name('provider_expenses.destroy');
        Route::prefix('files')->group(function () {
            Route::get('', GetProviderExpenseFiles::class)->name('provider_expense_files.index');
            // Route::get('download', DownloadAllProviderExpenseFiles::class)->name('provider_expense_files.download_all');
            Route::prefix('{provider_expense_file}')->group(function () {
                Route::get('download', DownloadProviderExpenseFile::class)->name('provider_expense_files.download');
            });
        });
    });
});

Route::prefix('provider-qualifications')->group(function () {
    Route::get('', GetProviderQualifications::class)->name('provider_qualifications.index');
    Route::get('create', CreateProviderQualification::class)->name('provider_qualifications.create');
    Route::post('', CreateProviderQualification::class)->name('provider_qualifications.store');
    Route::delete('delete-batch', DeleteProviderQualifications::class)->name('provider_qualifications.delete_batch');
    Route::prefix('{provider_qualification}')->group(function () {
        Route::get('', GetProviderQualification::class)->name('provider_qualifications.show');
        Route::get('edit', EditProviderQualification::class)->name('provider_qualifications.edit');
        Route::put('', EditProviderQualification::class)->name('provider_qualifications.update');
    });
});

Route::prefix('provider-qualification-criteria')->group(function () {
    Route::get('', GetProviderQualificationCriteria::class)->name('provider_qualification_criteria.index');
    Route::get('create', CreateProviderQualificationCriterion::class)->name('provider_qualification_criteria.create');
    Route::post('', CreateProviderQualificationCriterion::class)->name('provider_qualification_criteria.store');
    Route::delete('delete-batch', DeleteProviderQualificationCriteria::class)->name('provider_qualification_criteria.delete_batch');
    Route::prefix('{provider_qualification_criterion}')->group(function () {
        Route::get('', GetProviderQualificationCriterion::class)->name('provider_qualification_criteria.show');
        Route::get('edit', EditProviderQualificationCriterion::class)->name('provider_qualification_criteria.edit');
        Route::put('', EditProviderQualificationCriterion::class)->name('provider_qualification_criteria.update');
    });
});
