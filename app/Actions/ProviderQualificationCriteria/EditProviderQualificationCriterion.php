<?php

namespace App\Actions\ProviderQualificationCriteria;

use App\Models\Permission;
use App\Models\ProviderQualificationCriterion;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Lorisleiva\Actions\ActionRequest;
use Lorisle<PERSON>\Actions\Concerns\AsAction;
use Throwable;

class EditProviderQualificationCriterion
{
    use AsAction;

    protected array $rules = [
        'name' => 'required',
        'sequence' => 'required',
        'min_grade' => 'required',
        'max_grade' => 'required',
        'step' => 'required',
        'weight' => 'required',
    ];

    protected array $messages = [
        'name.required' => 'É obrigatório informar o nome.',
        'sequence.required' => 'É obrigatório informar a sequência.',
        'min_grade.required' => 'É obrigatório informar a nota mínima.',
        'max_grade.required' => 'É obrigatório informar a nota máxima.',
        'step.required' => 'É obrigatório informar o passo.',
        'weight.required' => 'É obrigatório informar o peso.',
    ];

    public function authorize(ActionRequest $request): bool
    {
        return $request->user()->can(Permission::UPDATE_PROVIDER_QUALIFICATION_CRITERIA);
    }

    public function asController(ActionRequest $request, ProviderQualificationCriterion $providerQualificationCriterion): mixed
    {
        if ($request->method() === Request::METHOD_GET) {
            return $providerQualificationCriterion->getBackendActionGenerator()->loadEditView(
                compact('providerQualificationCriterion')
            );
        }

        $validator = Validator::make($request->all(), $this->rules, $this->messages);
        $validator->validate();

        try {
            $this->handle($providerQualificationCriterion, $validator->validated());
            return redirect_success('provider_qualification_criteria.index', __('provider_qualification_criteria.responses.update.success'));
        } catch (Throwable $th) {
            return redirect_error($th->getMessage());
        }
    }

    public function handle(ProviderQualificationCriterion $providerQualificationCriterion, array $data): ProviderQualificationCriterion
    {
        try {
            $providerQualificationCriterion->update($data);
            return $providerQualificationCriterion;
        } catch (Throwable $th) {
            throw_error($th);
        }
    }
}
