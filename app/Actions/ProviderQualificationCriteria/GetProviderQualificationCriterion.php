<?php

namespace App\Actions\ProviderQualificationCriteria;

use App\Models\Permission;
use App\Models\ProviderQualificationCriterion;
use Illuminate\Contracts\View\Factory;
use Illuminate\Contracts\View\View;
use Lorisleiva\Actions\ActionRequest;
use Lorisle<PERSON>\Actions\Concerns\AsAction;
use Throwable;

class GetProviderQualificationCriterion
{
    use AsAction;

    public function authorize(ActionRequest $request): bool
    {
        return $request->user()->can(Permission::GET_PROVIDER_QUALIFICATION_CRITERIA);
    }

    public function handle(ProviderQualificationCriterion $providerQualificationCriterion): Factory|View
    {
        try {
            return $providerQualificationCriterion->getBackEndActionGeneratorInstance()->loadShowView(
                compact('providerQualificationCriterion')
            );
        } catch (Throwable $th) {
            throw_error($th);
        }
    }
}
