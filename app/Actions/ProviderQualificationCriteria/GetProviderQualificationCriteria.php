<?php

namespace App\Actions\ProviderQualificationCriteria;

use App\Models\Permission;
use App\Models\ProviderQualificationCriterion;
use Illuminate\Contracts\View\Factory;
use Illuminate\Contracts\View\View;
use Lorisleiva\Actions\ActionRequest;
use Lorisleiva\Actions\Concerns\AsAction;

class GetProviderQualificationCriteria
{
    use AsAction;

    public function authorize(ActionRequest $request): bool
    {
        return $request->user()->can(Permission::GET_PROVIDER_QUALIFICATION_CRITERIA);
    }

    public function handle(): Factory|View
    {
        return ProviderQualificationCriterion::getBackEndActionGenerator()->loadIndexView();
    }
}
