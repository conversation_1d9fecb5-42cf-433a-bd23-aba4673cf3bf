<?php

namespace App\Actions\ProviderQualificationCriteria;

use App\Models\ProviderQualificationCriterion;
use Lorisleiva\Actions\Concerns\AsAction;
use Throwable;

class DeleteProviderQualificationCriterion
{
    use AsAction;

    public function handle(ProviderQualificationCriterion $providerQualificationCriterion)
    {
        try {
            $providerQualificationCriterion->delete();
        } catch (Throwable $th) {
            throw_error($th);
        }
    }
}
