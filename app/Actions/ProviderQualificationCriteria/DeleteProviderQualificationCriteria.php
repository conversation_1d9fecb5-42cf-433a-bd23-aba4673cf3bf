<?php

namespace App\Actions\ProviderQualificationCriteria;

use App\Core\Http\Requests\DeleteBatchRequest;
use App\Models\Permission;
use App\Models\ProviderQualificationCriterion;
use Illuminate\Http\RedirectResponse;
use Lorisle<PERSON>\Actions\ActionRequest;
use Lorisle<PERSON>\Actions\Concerns\AsAction;
use Throwable;

class DeleteProviderQualificationCriteria
{
    use AsAction;

    public function authorize(ActionRequest $request): bool
    {
        return $request->user()->can(Permission::DELETE_PROVIDER_QUALIFICATION_CRITERIA);
    }

    public function asController(DeleteBatchRequest $request): RedirectResponse
    {
        try {
            $this->handle($request->getDeleteIdsArray());
            return delete_batch_redirect_success('provider_qualification_criteria.index');
        } catch (Throwable $th) {
            return redirect_error($th->getMessage());
        }
    }

    public function handle(array $ids)
    {
        try {
            collect($ids)->each(function (string $id): void {
                DeleteProviderQualificationCriterion::run(ProviderQualificationCriterion::find($id));
            });
        } catch (Throwable $th) {
            throw_error($th);
        }
    }
}
