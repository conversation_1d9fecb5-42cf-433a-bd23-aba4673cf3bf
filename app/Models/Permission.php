<?php

namespace App\Models;

/**
 * Permission model.
 *
 * @package App\Models
 */
class Permission extends \Spatie\Permission\Models\Permission
{
    public const GET_ROLES = 'get_roles';
    public const CREATE_ROLES = 'create_roles';
    public const UPDATE_ROLES = 'update_roles';
    public const DELETE_ROLES = 'delete_roles';

    public const GET_OPERATORS = 'get_operators';
    public const CREATE_OPERATORS = 'create_operators';
    public const UPDATE_OPERATORS = 'update_operators';
    public const DELETE_OPERATORS = 'delete_operators';

    public const GET_TEAMS = 'get_teams';
    public const CREATE_TEAMS = 'create_teams';
    public const UPDATE_TEAMS = 'update_teams';
    public const DELETE_TEAMS = 'delete_teams';

    public const GET_COMMISSION_RULES = 'get_commission_rules';
    public const CREATE_COMMISSION_RULES = 'create_commission_rules';
    public const UPDATE_COMMISSION_RULES = 'update_commission_rules';
    public const DELETE_COMMISSION_RULES = 'delete_commission_rules';

    public const GET_HOLIDAYS = 'get_holidays';
    public const CREATE_HOLIDAYS = 'create_holidays';
    public const UPDATE_HOLIDAYS = 'update_holidays';
    public const DELETE_HOLIDAYS = 'delete_holidays';

    public const GET_INDICES = 'get_indices';
    public const CREATE_INDICES = 'create_indices';
    public const UPDATE_INDICES = 'update_indices';
    public const DELETE_INDICES = 'delete_indices';

    public const GET_PROCEDURES = 'get_procedures';
    public const CREATE_PROCEDURES = 'create_procedures';
    public const UPDATE_PROCEDURES = 'update_procedures';
    public const DELETE_PROCEDURES = 'delete_procedures';

    public const GET_EXPENSE_TYPES = 'get_expense_types';
    public const CREATE_EXPENSE_TYPES = 'create_expense_types';
    public const UPDATE_EXPENSE_TYPES = 'update_expense_types';
    public const DELETE_EXPENSE_TYPES = 'delete_expense_types';

    public const GET_CANCELLATION_REASONS = 'get_cancellation_reasons';
    public const CREATE_CANCELLATION_REASONS = 'create_cancellation_reasons';
    public const UPDATE_CANCELLATION_REASONS = 'update_cancellation_reasons';
    public const DELETE_CANCELLATION_REASONS = 'delete_cancellation_reasons';

    public const GET_CONTRACT_TYPES = 'get_contract_types';
    public const CREATE_CONTRACT_TYPES = 'create_contract_types';
    public const UPDATE_CONTRACT_TYPES = 'update_contract_types';
    public const DELETE_CONTRACT_TYPES = 'delete_contract_types';

    public const GET_COMPANIES = 'get_companies';
    public const CREATE_COMPANIES = 'create_companies';
    public const UPDATE_COMPANIES = 'update_companies';
    public const DELETE_COMPANIES = 'delete_companies';

    public const GET_COMPANY_CONTACTS = 'get_company_contacts';
    public const CREATE_COMPANY_CONTACTS = 'create_company_contacts';
    public const UPDATE_COMPANY_CONTACTS = 'update_company_contacts';
    public const DELETE_COMPANY_CONTACTS = 'delete_company_contacts';

    public const GET_COMPANY_GROUPS = 'get_company_groups';
    public const CREATE_COMPANY_GROUPS = 'create_company_groups';
    public const UPDATE_COMPANY_GROUPS = 'update_company_groups';
    public const DELETE_COMPANY_GROUPS = 'delete_company_groups';

    public const GET_COMPANY_GROUP_COMPANIES = 'get_company_group_companies';
    public const CREATE_COMPANY_GROUP_COMPANIES = 'create_company_group_companies';
    public const UPDATE_COMPANY_GROUP_COMPANIES = 'update_company_group_companies';
    public const DELETE_COMPANY_GROUP_COMPANIES = 'delete_company_group_companies';

    public const GET_SUPPLIERS = 'get_suppliers';
    public const CREATE_SUPPLIERS = 'create_suppliers';
    public const UPDATE_SUPPLIERS = 'update_suppliers';
    public const DELETE_SUPPLIERS = 'delete_suppliers';

    public const GET_SUPPLIER_EXPENSE_TYPES = 'get_supplier_expense_types';
    public const CREATE_SUPPLIER_EXPENSE_TYPES = 'create_supplier_expense_types';
    public const UPDATE_SUPPLIER_EXPENSE_TYPES = 'update_supplier_expense_types';
    public const DELETE_SUPPLIER_EXPENSE_TYPES = 'delete_supplier_expense_types';

    public const GET_PROVIDERS = 'get_providers';
    public const CREATE_PROVIDERS = 'create_providers';
    public const UPDATE_PROVIDERS = 'update_providers';
    public const DELETE_PROVIDERS = 'delete_providers';

    public const GET_PROVIDER_FILES = 'get_provider_files';
    public const CREATE_PROVIDER_FILES = 'create_provider_files';
    public const UPDATE_PROVIDER_FILES = 'update_provider_files';
    public const DELETE_PROVIDER_FILES = 'delete_provider_files';

    public const GET_PROVIDER_CONTACTS = 'get_provider_contacts';
    public const CREATE_PROVIDER_CONTACTS = 'create_provider_contacts';
    public const UPDATE_PROVIDER_CONTACTS = 'update_provider_contacts';
    public const DELETE_PROVIDER_CONTACTS = 'delete_provider_contacts';

    public const GET_PROVIDER_PROCEDURES = 'get_provider_procedures';
    public const CREATE_PROVIDER_PROCEDURES = 'create_provider_procedures';
    public const UPDATE_PROVIDER_PROCEDURES = 'update_provider_procedures';
    public const DELETE_PROVIDER_PROCEDURES = 'delete_provider_procedures';

    public const GET_PROVIDER_EXPENSE_TYPES = 'get_provider_expense_types';
    public const CREATE_PROVIDER_EXPENSE_TYPES = 'create_provider_expense_types';
    public const UPDATE_PROVIDER_EXPENSE_TYPES = 'update_provider_expense_types';
    public const DELETE_PROVIDER_EXPENSE_TYPES = 'delete_provider_expense_types';

    public const GET_PROVIDER_COMPANIES = 'get_provider_companies';
    public const CREATE_PROVIDER_COMPANIES = 'create_provider_companies';
    public const UPDATE_PROVIDER_COMPANIES = 'update_provider_companies';
    public const DELETE_PROVIDER_COMPANIES = 'delete_provider_companies';

    public const GET_CONTRACTS = 'get_contracts';
    public const CREATE_CONTRACTS = 'create_contracts';
    public const UPDATE_CONTRACTS = 'update_contracts';
    public const DELETE_CONTRACTS = 'delete_contracts';

    public const GET_COMPANY_CLOSING_GLOSSES = 'get_company_closing_glosses';
    public const CREATE_COMPANY_CLOSING_GLOSSES = 'create_company_closing_glosses';
    public const UPDATE_COMPANY_CLOSING_GLOSSES = 'update_company_closing_glosses';
    public const DELETE_COMPANY_CLOSING_GLOSSES = 'delete_company_closing_glosses';

    public const GET_SUPPLIER_EXPENSES = 'get_supplier_expenses';
    public const CREATE_SUPPLIER_EXPENSES = 'create_supplier_expenses';
    public const UPDATE_SUPPLIER_EXPENSES = 'update_supplier_expenses';
    public const DELETE_SUPPLIER_EXPENSES = 'delete_supplier_expenses';
    public const APPROVE_SUPPLIER_EXPENSES = 'approve_supplier_expenses';

    public const GET_PROVIDER_EXPENSES = 'get_provider_expenses';
    public const CREATE_PROVIDER_EXPENSES = 'create_provider_expenses';
    public const UPDATE_PROVIDER_EXPENSES = 'update_provider_expenses';
    public const DELETE_PROVIDER_EXPENSES = 'delete_provider_expenses';
    public const APPROVE_PROVIDER_EXPENSES = 'approve_provider_expenses';

    public const GET_GROUPED_EXPENSES = 'get_grouped_expenses';

    public const GET_COMPANY_CLOSINGS = 'get_company_closings';
    public const CREATE_COMPANY_CLOSINGS = 'create_company_closings';
    public const UPDATE_COMPANY_CLOSINGS = 'update_company_closings';
    public const DELETE_COMPANY_CLOSINGS = 'delete_company_closings';

    public const GET_COMPANY_FOLLOW_UPS = 'get_company_follow_ups';
    public const CREATE_COMPANY_FOLLOW_UPS = 'create_company_follow_ups';
    public const UPDATE_COMPANY_FOLLOW_UPS = 'update_company_follow_ups';
    public const DELETE_COMPANY_FOLLOW_UPS = 'delete_company_follow_ups';

    public const GET_STAND_ALONE_CONTRACTS_FOR_BILLING = 'get_stand_alone_contracts_for_billing';

    public const GET_BILLING_PANEL = 'get_billing_panel';

    public const GET_IMPORTS = 'get_imports';
    public const CREATE_IMPORTS = 'create_imports';
    public const UPDATE_IMPORTS = 'update_imports';
    public const DELETE_IMPORTS = 'delete_imports';

    public const GET_EXAMS = 'get_exams';
    public const CREATE_EXAMS = 'create_exams';
    public const UPDATE_EXAMS = 'update_exams';
    public const DELETE_EXAMS = 'delete_exams';

    public const GET_SERVICE_ORDERS = 'get_service_orders';
    public const CREATE_SERVICE_ORDERS = 'create_service_orders';
    public const UPDATE_SERVICE_ORDERS = 'update_service_orders';
    public const DELETE_SERVICE_ORDERS = 'delete_service_orders';

    public const GET_WORKFLOWS = 'get_workflows';
    public const CREATE_WORKFLOWS = 'create_workflows';
    public const UPDATE_WORKFLOWS = 'update_workflows';
    public const DELETE_WORKFLOWS = 'delete_workflows';

    public const GET_SCHEDULES = 'get_schedules';
    public const CREATE_SCHEDULES = 'create_schedules';
    public const UPDATE_SCHEDULES = 'update_schedules';
    public const DELETE_SCHEDULES = 'delete_schedules';

    public const GET_RECEIVABLES = 'get_receivables';
    public const UPDATE_RECEIVABLES = 'update_receivables';

    public const GET_BILLING_ENTRIES = 'get_billing_entries';
    public const CREATE_BILLING_ENTRIES = 'create_billing_entries';
    public const UPDATE_BILLING_ENTRIES = 'update_billing_entries';
    public const DELETE_BILLING_ENTRIES = 'delete_billing_entries';

    public const GET_CATEGORIES = 'get_categories';
    public const CREATE_CATEGORIES = 'create_categories';
    public const UPDATE_CATEGORIES = 'update_categories';
    public const DELETE_CATEGORIES = 'delete_categories';

    public const GET_SUBCATEGORIES = 'get_subcategories';
    public const CREATE_SUBCATEGORIES = 'create_subcategories';
    public const UPDATE_SUBCATEGORIES = 'update_subcategories';
    public const DELETE_SUBCATEGORIES = 'delete_subcategories';

    public const GET_COMPANY_CLOSING_GROUPS = 'get_company_closing_groups';
    public const CREATE_COMPANY_CLOSING_GROUPS = 'create_company_closing_groups';
    public const UPDATE_COMPANY_CLOSING_GROUPS = 'update_company_closing_groups';
    public const DELETE_COMPANY_CLOSING_GROUPS = 'delete_company_closing_groups';

    public const GET_CONTRACT_BILLING_GROUPS = 'get_contract_billing_groups';
    public const CREATE_CONTRACT_BILLING_GROUPS = 'create_contract_billing_groups';
    public const UPDATE_CONTRACT_BILLING_GROUPS = 'update_contract_billing_groups';
    public const DELETE_CONTRACT_BILLING_GROUPS = 'delete_contract_billing_groups';

    public const GET_PRE_COMPANIES = 'get_pre_companies';
    public const CREATE_PRE_COMPANIES = 'create_pre_companies';
    public const UPDATE_PRE_COMPANIES = 'update_pre_companies';
    public const DELETE_PRE_COMPANIES = 'delete_pre_companies';

    public const GET_CHECKLISTS = 'get_checklists';
    public const CREATE_CHECKLISTS = 'create_checklists';
    public const UPDATE_CHECKLISTS = 'update_checklists';
    public const DELETE_CHECKLISTS = 'delete_checklists';

    public const GET_CHECKLIST_PHASES = 'get_checklist_phases';
    public const CREATE_CHECKLIST_PHASES = 'create_checklist_phases';
    public const UPDATE_CHECKLIST_PHASES = 'update_checklist_phases';
    public const DELETE_CHECKLIST_PHASES = 'delete_checklist_phases';

    public const GET_CHECKLIST_PHASE_ITEMS = 'get_checklist_phase_items';
    public const CREATE_CHECKLIST_PHASE_ITEMS = 'create_checklist_phase_items';
    public const UPDATE_CHECKLIST_PHASE_ITEMS = 'update_checklist_phase_items';
    public const DELETE_CHECKLIST_PHASE_ITEMS = 'delete_checklist_phase_items';

    public const GET_PROVIDER_CITY_COVERAGE_CITIES = 'get_provider_city_coverage_cities';
    public const CREATE_PROVIDER_CITY_COVERAGE_CITIES = 'create_provider_city_coverage_cities';
    public const UPDATE_PROVIDER_CITY_COVERAGE_CITIES = 'update_provider_city_coverage_cities';
    public const DELETE_PROVIDER_CITY_COVERAGE_CITIES = 'delete_provider_city_coverage_cities';

    public const GET_LEADS = 'get_leads';
    public const CREATE_LEADS = 'create_leads';
    public const UPDATE_LEADS = 'update_leads';
    public const DELETE_LEADS = 'delete_leads';

    public const GET_LEAD_COMPANIES = 'get_lead_companies';
    public const CREATE_LEAD_COMPANIES = 'create_lead_companies';
    public const UPDATE_LEAD_COMPANIES = 'update_lead_companies';
    public const DELETE_LEAD_COMPANIES = 'delete_lead_companies';

    public const GET_CRM_FUNNEL_TYPES = 'get_crm_funnel_types';
    public const CREATE_CRM_FUNNEL_TYPES = 'create_crm_funnel_types';
    public const UPDATE_CRM_FUNNEL_TYPES = 'update_crm_funnel_types';
    public const DELETE_CRM_FUNNEL_TYPES = 'delete_crm_funnel_types';

    public const GET_CRM_FUNNELS = 'get_crm_funnels';
    public const CREATE_CRM_FUNNELS = 'create_crm_funnels';
    public const UPDATE_CRM_FUNNELS = 'update_crm_funnels';
    public const DELETE_CRM_FUNNELS = 'delete_crm_funnels';

    public const GET_CRM_FUNNEL_STEPS = 'get_crm_funnel_steps';
    public const CREATE_CRM_FUNNEL_STEPS = 'create_crm_funnel_steps';
    public const UPDATE_CRM_FUNNEL_STEPS = 'update_crm_funnel_steps';
    public const DELETE_CRM_FUNNEL_STEPS = 'delete_crm_funnel_steps';

    public const GET_CRM_FUNNEL_OPERATORS = 'get_crm_funnel_operators';
    public const CREATE_CRM_FUNNEL_OPERATORS = 'create_crm_funnel_operators';
    public const UPDATE_CRM_FUNNEL_OPERATORS = 'update_crm_funnel_operators';
    public const DELETE_CRM_FUNNEL_OPERATORS = 'delete_crm_funnel_operators';

    public const GET_CRM_CITY_COVERAGES = 'get_crm_city_coverages';
    public const CREATE_CRM_CITY_COVERAGES = 'create_crm_city_coverages';
    public const UPDATE_CRM_CITY_COVERAGES = 'update_crm_city_coverages';
    public const DELETE_CRM_CITY_COVERAGES = 'delete_crm_city_coverages';

    public const GET_CRM_CITY_COVERAGE_CITIES = 'get_crm_city_coverage_cities';
    public const CREATE_CRM_CITY_COVERAGE_CITIES = 'create_crm_city_coverage_cities';
    public const UPDATE_CRM_CITY_COVERAGE_CITIES = 'update_crm_city_coverage_cities';
    public const DELETE_CRM_CITY_COVERAGE_CITIES = 'delete_crm_city_coverage_cities';

    public const GET_PROCEDURE_CRM_CITY_COVERAGES = 'get_procedure_crm_city_coverages';
    public const CREATE_PROCEDURE_CRM_CITY_COVERAGES = 'create_procedure_crm_city_coverages';
    public const UPDATE_PROCEDURE_CRM_CITY_COVERAGES = 'update_procedure_crm_city_coverages';
    public const DELETE_PROCEDURE_CRM_CITY_COVERAGES = 'delete_procedure_crm_city_coverages';

    public const GET_PROCEDURE_CRM_CITY_COVERAGE_RANGES = 'get_procedure_crm_city_coverage_ranges';
    public const CREATE_PROCEDURE_CRM_CITY_COVERAGE_RANGES = 'create_procedure_crm_city_coverage_ranges';
    public const UPDATE_PROCEDURE_CRM_CITY_COVERAGE_RANGES = 'update_procedure_crm_city_coverage_ranges';
    public const DELETE_PROCEDURE_CRM_CITY_COVERAGE_RANGES = 'delete_procedure_crm_city_coverage_ranges';

    public const GET_PROCEDURE_PAYMENT_METHODS = 'get_procedure_payment_methods';
    public const CREATE_PROCEDURE_PAYMENT_METHODS = 'create_procedure_payment_methods';
    public const UPDATE_PROCEDURE_PAYMENT_METHODS = 'update_procedure_payment_methods';
    public const DELETE_PROCEDURE_PAYMENT_METHODS = 'delete_procedure_payment_methods';

    public const GET_REASONS = 'get_reasons';
    public const CREATE_REASONS = 'create_reasons';
    public const UPDATE_REASONS = 'update_reasons';
    public const DELETE_REASONS = 'delete_reasons';

    public const GET_CRM_ORIGINS = 'get_crm_origins';
    public const CREATE_CRM_ORIGINS = 'create_crm_origins';
    public const UPDATE_CRM_ORIGINS = 'update_crm_origins';
    public const DELETE_CRM_ORIGINS = 'delete_crm_origins';

    public const GET_ARTICLE_CATEGORIES = 'get_article_categories';
    public const CREATE_ARTICLE_CATEGORIES = 'create_article_categories';
    public const UPDATE_ARTICLE_CATEGORIES = 'update_article_categories';
    public const DELETE_ARTICLE_CATEGORIES = 'delete_article_categories';

    public const GET_ARTICLE_TAGS = 'get_article_tags';
    public const CREATE_ARTICLE_TAGS = 'create_article_tags';
    public const UPDATE_ARTICLE_TAGS = 'update_article_tags';
    public const DELETE_ARTICLE_TAGS = 'delete_article_tags';

    public const GET_ARTICLES = 'get_articles';
    public const CREATE_ARTICLES = 'create_articles';
    public const UPDATE_ARTICLES = 'update_articles';
    public const DELETE_ARTICLES = 'delete_articles';

    public const GET_TICKET_CATEGORIES = 'get_ticket_categories';
    public const CREATE_TICKET_CATEGORIES = 'create_ticket_categories';
    public const UPDATE_TICKET_CATEGORIES = 'update_ticket_categories';
    public const DELETE_TICKET_CATEGORIES = 'delete_ticket_categories';

    public const GET_TICKETS = 'get_tickets';
    public const CREATE_TICKETS = 'create_tickets';
    public const UPDATE_TICKETS = 'update_tickets';
    public const DELETE_TICKETS = 'delete_tickets';

    public const GET_ARTICLE_RECORD_PROTECTIONS = 'get_article_record_protections';
    public const CREATE_ARTICLE_RECORD_PROTECTIONS = 'create_article_record_protections';
    public const UPDATE_ARTICLE_RECORD_PROTECTIONS = 'update_article_record_protections';
    public const DELETE_ARTICLE_RECORD_PROTECTIONS = 'delete_article_record_protections';

    public const GET_ARTICLE_RECORD_RETENTIONS = 'get_article_record_retentions';
    public const CREATE_ARTICLE_RECORD_RETENTIONS = 'create_article_record_retentions';
    public const UPDATE_ARTICLE_RECORD_RETENTIONS = 'update_article_record_retentions';
    public const DELETE_ARTICLE_RECORD_RETENTIONS = 'delete_article_record_retentions';

    public const GET_CRM_CONVERSIONS = 'get_crm_conversions';
    public const CREATE_CRM_CONVERSIONS = 'create_crm_conversions';
    public const UPDATE_CRM_CONVERSIONS = 'update_crm_conversions';
    public const DELETE_CRM_CONVERSIONS = 'delete_crm_conversions';

    public const GET_PROVIDER_QUALIFICATION_CRITERIA = 'get_provider_qualification_criteria';
    public const CREATE_PROVIDER_QUALIFICATION_CRITERIA = 'create_provider_qualification_criteria';
    public const UPDATE_PROVIDER_QUALIFICATION_CRITERIA = 'update_provider_qualification_criteria';
    public const DELETE_PROVIDER_QUALIFICATION_CRITERIA = 'delete_provider_qualification_criteria';

    public const GET_PROVIDER_QUALIFICATIONS = 'get_provider_qualifications';
    public const CREATE_PROVIDER_QUALIFICATIONS = 'create_provider_qualifications';
    public const UPDATE_PROVIDER_QUALIFICATIONS = 'update_provider_qualifications';
    public const DELETE_PROVIDER_QUALIFICATIONS = 'delete_provider_qualifications';

    public const GET_SERVICE_ORDERS_REPORT = 'get_service_orders_report';
    public const GET_CONTRACTS_REPORT = 'get_contracts_report';
    public const GET_CONTRACTS_WITHOUT_SERVICE_ORDERS_REPORT = 'get_contracts_without_service_orders_report';
    public const GET_PROVIDERS_REPORT = 'get_providers_report';
    public const GET_SUPPLIERS_REPORT = 'get_suppliers_report';
    public const GET_COMMISSION_PAYMENT_REPORT = 'get_commission_payment_report';
    public const GET_COMPANIES_REPORT = 'get_companies_report';
    public const GET_COMPANIES_WITH_LIFE_COUNT_REPORT = 'get_companies_with_life_count_report';
    public const GET_COMPANY_LOSS_RATIO_REPORT = 'get_company_loss_ratio_report';
    public const GET_PROVIDER_LOSS_RATIO_REPORT = 'get_provider_loss_ratio_report';
    public const GET_PROVIDER_PROCEDURES_REPORT = 'get_provider_procedures_report';
    public const GET_PROVIDER_COMPANIES_REPORT = 'get_provider_companies_report';
    public const GET_PENDING_BILLING_CONTRACTS_FOR_PERIOD_REPORT = 'get_pending_billing_contracts_for_period_report';
    public const GET_OPEN_RECEIVABLES_REPORT = 'get_open_receivables_report';
    public const GET_PROVIDER_EXPENSES_REPORT = 'get_provider_expenses_report';
    public const GET_SUPPLIER_EXPENSES_REPORT = 'get_supplier_expenses_report';
    public const GET_TICKETS_REPORT = 'get_tickets_report';

    public const CREATE_EMPLOYEE_INACTIVITY_IMPORT = 'create_employee_inactivity_import';

    /**
     * Translate a given permission.
     *
     * @param string $permission
     * @return array|\Illuminate\Contracts\Foundation\Application|\Illuminate\Contracts\Translation\Translator|string
     */
    public static function translate(string $permission)
    {
        return __('permissions.' . $permission);
    }

    /**
     * Get all available permissions.
     *
     * @return array
     */
    public static function getAvailablePermissions(): array
    {
        $permissionReflectionClass = new \ReflectionClass(self::class);

        return array_filter(
            array_values($permissionReflectionClass->getConstants()),
            fn ($item) => !in_array($item, ['created_at', 'updated_at'])
        );
    }
}
