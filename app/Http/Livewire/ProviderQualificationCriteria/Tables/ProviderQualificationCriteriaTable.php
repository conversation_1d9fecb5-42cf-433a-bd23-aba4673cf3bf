<?php

namespace App\Http\Livewire\ProviderQualificationCriteria\Tables;

use Rappasoft\LaravelLivewireTables\DataTableComponent;
use Rappasoft\LaravelLivewireTables\Views\Column;
use App\Models\QualificationCriterion;

class ProviderQualificationCriteriaTable extends DataTableComponent
{
    protected $model = QualificationCriterion::class;

    public function configure(): void
    {
        $this->setPrimaryKey('id');
    }

    public function columns(): array
    {
        return [
            Column::make("Id", "id")
                ->sortable(),
            Column::make("Name", "name")
                ->sortable(),
            Column::make("Type", "type")
                ->sortable(),
            Column::make("Sequence", "sequence")
                ->sortable(),
            Column::make("Min grade", "min_grade")
                ->sortable(),
            Column::make("Max grade", "max_grade")
                ->sortable(),
            Column::make("Step", "step")
                ->sortable(),
            Column::make("Created at", "created_at")
                ->sortable(),
            Column::make("Updated at", "updated_at")
                ->sortable(),
        ];
    }
}
